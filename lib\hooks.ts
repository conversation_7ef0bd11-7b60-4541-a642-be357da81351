import useSWR from 'swr';
import { useSession } from '../context/session';
import { ErrorProps, ListItem, Order, QueryParams, ShippingAndProductsInfo } from '../types';
import { FeeRuleData } from '../types/db';
import { Brand } from '../pages/api/catalog/brands';
import { Category } from '../pages/api/catalog/categories';
import { CustomerGroup } from '../pages/api/customers/groups';
import { Channel } from '../pages/api/channels';
import { Product } from '../pages/api/catalog/products';
import { useState, useCallback } from 'react';

async function fetcher(url: string, query: string) {
    const res = await fetch(`${url}?${query}`);

    // If the status code is not in the range 200-299, throw an error
    if (!res.ok) {
        const { message } = await res.json();
        const error: ErrorProps = new Error(message || 'An error occurred while fetching the data.');
        error.status = res.status; // e.g. 500
        throw error;
    }

    return res.json();
}

// Reusable SWR hooks
// https://swr.vercel.app/
export function useProducts() {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();
    // Request is deduped and cached; Can be shared across components
    const { data, error } = useSWR(context ? ['/api/products', params] : null, fetcher);

    return {
        summary: data,
        isLoading: !data && !error,
        error,
    };
}

export function useProductList(query?: QueryParams) {
    const { context } = useSession();
    const params = new URLSearchParams({ ...query, context }).toString();

    // Use an array to send multiple arguments to fetcher
    const { data, error, mutate: mutateList } = useSWR(context ? ['/api/products/list', params] : null, fetcher);

    return {
        list: data?.data,
        meta: data?.meta,
        isLoading: !data && !error,
        error,
        mutateList,
    };
}

export function useProductInfo(pid: number, list?:ListItem[]) {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();

    let product: ListItem; 

    if (list?.length) { 
       product = list.find(item => item.id === pid);
    }

    // Conditionally fetch product if it doesn't exist in the list (e.g. deep linking)
    const { data, error } = useSWR(!product && context ? [`/api/products/${pid}`, params] : null, fetcher);

    return {
        product: product ?? data,
        isLoading: product ? false : (!data && !error),
        error,
    };
}

export const useOrder = (orderId: number) => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();
    const shouldFetch = context && orderId !== undefined;

    // Conditionally fetch orderId is defined
    const { data, error } = useSWR<Order, ErrorProps>(shouldFetch ? [`/api/orders/${orderId}`, params] : null, fetcher);

    return {
        order: data,
        isLoading: !data && !error,
        error,
    };
}

export const useShippingAndProductsInfo = (orderId: number) => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();
    const shouldFetch = context && orderId !== undefined;

    // Shipping addresses and products are not included in the order data and need to be fetched separately
    const { data, error } = useSWR<ShippingAndProductsInfo, ErrorProps>(
        shouldFetch ? [`/api/orders/${orderId}/shipping_products`, params] : null, fetcher
    );

    return {
        order: data,
        isLoading: !data && !error,
        error,
    };
}

export const useFeeRules = () => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();

    // Request is deduped and cached; Can be shared across components
    const { data, error, mutate } = useSWR(context ? ['/api/fee-rules', params] : null, fetcher);

    return {
        feeRules: data?.data || [],
        isLoading: !data && !error,
        error,
        mutate,
    };
}

export const useFeeRule = (feeRuleId: string) => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();

    // Request is deduped and cached; Can be shared across components
    const { data, error, mutate } = useSWR(
        context && feeRuleId ? [`/api/fee-rules/${feeRuleId}`, params] : null,
        fetcher
    );

    return {
        feeRule: data?.data || null,
        isLoading: !data && !error,
        error,
        mutate,
    };
}

export const useBrands = () => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();

    // Request is deduped and cached; Can be shared across components
    const { data, error } = useSWR(context ? ['/api/catalog/brands', params] : null, fetcher);
console.log("data brands",data);

    return {
        brands: data?.data || [],
        isLoading: !data && !error,
        error: data?.fallback ? data.error : error,
        fallback: data?.fallback || false,
    };
}

export const useCategories = () => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();

    // Request is deduped and cached; Can be shared across components
    const { data, error } = useSWR(context ? ['/api/catalog/categories', params] : null, fetcher);
    console.log("data categories",data);
    return {
        categories: data?.data || [],
        isLoading: !data && !error,
        error: data?.fallback ? data.error : error,
        fallback: data?.fallback || false,
    };
}

export const useCustomerGroups = () => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();

    // Request is deduped and cached; Can be shared across components
    const { data, error } = useSWR(context ? ['/api/customers/groups', params] : null, fetcher);
    console.log("data customer groups", data);

    return {
        customerGroups: data?.data || [],
        isLoading: !data && !error,
        error: data?.fallback ? data.error : error,
        fallback: data?.fallback || false,
    };
}

export const useChannels = () => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();

    // Request is deduped and cached; Can be shared across components
    const { data, error } = useSWR(context ? ['/api/channels', params] : null, fetcher);
    console.log("data channels", data);

    return {
        channels: data?.data || [],
        isLoading: !data && !error,
        error: data?.fallback ? data.error : error,
        fallback: data?.fallback || false,
    };
}

export const useProductsForFilter = (loadAll: boolean = true) => {
    const { context } = useSession();
    const queryParams = new URLSearchParams({
        context,
        loadAll: loadAll ? 'true' : 'false'
    }).toString();

    // Request is deduped and cached; Can be shared across components
    const { data, error } = useSWR(
        context ? ['/api/catalog/products', queryParams] : null,
        fetcher
    );

    return {
        products: data?.data || [],
        meta: data?.meta,
        isLoading: !data && !error,
        error: data?.fallback ? data.error : error,
        fallback: data?.fallback || false,
        loadedAll: data?.meta?.loadedAll || false,
        hasMoreProducts: data?.meta?.hasMoreProducts || false,
        totalAvailable: data?.meta?.totalAvailable || 0,
        maxProductsLimit: data?.meta?.maxProductsLimit || 1000,
    };
}

export const useTaxClasses = () => {
    const { context } = useSession();
    const params = new URLSearchParams({ context }).toString();

    // Request is deduped and cached; Can be shared across components
    const { data, error } = useSWR(context ? ['/api/tax-classes', params] : null, fetcher);

    return {
        taxClasses: data?.data || [],
        isLoading: !data && !error,
        error,
    };
}

/**
 * Hook for validating fee rule name uniqueness
 */
export const useNameValidation = () => {
    const { context } = useSession();
    const [isValidating, setIsValidating] = useState(false);
    const [validationError, setValidationError] = useState<string>('');

    const validateName = useCallback(async (name: string, excludeFeeRuleId?: string) => {
        if (!context || !name?.trim()) {
            setValidationError('');
            return true;
        }

        setIsValidating(true);
        setValidationError('');

        try {
            const params = new URLSearchParams({ context }).toString();
            const response = await fetch(`/api/fee-rules/validate-name?${params}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name.trim(),
                    excludeFeeRuleId,
                }),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Validation failed');
            }

            if (!result.isValid) {
                setValidationError(result.error || 'Name validation failed');
                return false;
            }

            return true;
        } catch (error) {
            console.error('Name validation error:', error);
            setValidationError('Unable to validate name. Please try again.');
            return false;
        } finally {
            setIsValidating(false);
        }
    }, [context]);

    const clearValidationError = useCallback(() => {
        setValidationError('');
    }, []);

    return {
        validateName,
        isValidating,
        validationError,
        clearValidationError,
    };
};