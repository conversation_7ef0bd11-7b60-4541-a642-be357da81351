import {
    Box,
    Button,
    Checkbox,
    Flex,
    Form,
    FormGroup,
    H1,
    Input,
    Panel,
    Select,
    Text,
} from '@bigcommerce/big-design';
import { useRouter } from 'next/router';
import { FormEvent, useState, useEffect } from 'react';
import ErrorMessage from '../components/error';
import Loading from '../components/loading';
import CheckboxList from '../components/CheckboxList';
import ProductFilter from '../components/ProductFilter';
import { useSession } from '../context/session';
import { useBrands, useCategories, useCustomerGroups, useChannels, useTaxClasses, useNameValidation } from '../lib/hooks';

interface FeeFormData {
    name: string;
    type: 'percentage' | 'fixed';
    display_name: string;
    cost: number;
    source: string;
    tax_class_id: number | '';
    selectedBrands: number[];
    selectedCategories: number[];
    selectedCustomerGroups: number[];
    selectedChannels: number[];
    selectedProducts: number[];
    active: boolean;
}

interface FormErrors {
    [key: string]: string;
}

const CreateFee = () => {
    const router = useRouter();
    const { context } = useSession();
    const { brands, isLoading: brandsLoading, error: brandsError } = useBrands();
    const { categories, isLoading: categoriesLoading, error: categoriesError } = useCategories();
    const { customerGroups, isLoading: customerGroupsLoading, error: customerGroupsError } = useCustomerGroups();
    const { channels, isLoading: channelsLoading, error: channelsError } = useChannels();
    const { taxClasses, isLoading: taxClassesLoading, error: taxClassesError } = useTaxClasses();
    const { validateName, isValidating, validationError, clearValidationError } = useNameValidation();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string>('');
    
    const [formData, setFormData] = useState<FeeFormData>({
        name: '',
        type: 'percentage',
        display_name: '',
        cost: 0,
        source: 'APP', // Keep this default value
        tax_class_id: '',
        selectedBrands: [],
        selectedCategories: [],
        selectedCustomerGroups: [],
        selectedChannels: [],
        selectedProducts: [],
        active: true,
    });

    // Log customer groups when they're loaded
    useEffect(() => {
        if (customerGroups.length > 0) {
            console.log("Customer Groups in create-fee:", customerGroups);
        }
    }, [customerGroups]);

    // Log channels when they're loaded
    useEffect(() => {
        if (channels.length > 0) {
            console.log("Channels in create-fee:", channels);
        }
    }, [channels]);

    const [errors, setErrors] = useState<FormErrors>({});

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        // Include name validation error if it exists
        if (validationError) {
            newErrors.name = validationError;
        }

        if (!formData.display_name.trim()) {
            newErrors.display_name = 'Display name is required';
        }

        // Remove source validation since it's hardcoded now

        if (formData.cost === undefined || formData.cost < 0) {
            newErrors.cost = 'Cost must be a non-negative number';
        }

        if (formData.tax_class_id !== '' && (formData.tax_class_id < 0 || !Number.isInteger(formData.tax_class_id))) {
            newErrors.tax_class_id = 'Tax class ID must be a non-negative integer';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (field: keyof FeeFormData, value: any) => {
        setFormData(prev => ({ ...prev, [field]: value }));

        // Clear error for this field when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }

        // Clear validation error when user starts typing in name field
        if (field === 'name' && validationError) {
            clearValidationError();
        }
    };

    // Handle name field blur for validation
    const handleNameBlur = async () => {
        if (formData.name.trim()) {
            await validateName(formData.name);
        }
    };

    const handleSubmit = async (event: FormEvent) => {
        event.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setIsSubmitting(true);
        setSubmitError('');

        try {
            const submitData: any = {
                name: formData.name.trim(),
                type: formData.type,
                display_name: formData.display_name.trim(),
                cost: formData.cost,
                source: 'APP', // Always use 'APP' as the source
                active: formData.active,
                selectedBrands: formData.selectedBrands,
                selectedCategories: formData.selectedCategories,
                selectedCustomerGroups: formData.selectedCustomerGroups,
                selectedChannels: formData.selectedChannels,
                selectedProducts: formData.selectedProducts,
            };

            // Only include optional fields if they have values
            if (formData.tax_class_id !== '' && typeof formData.tax_class_id === 'number') {
                submitData.tax_class_id = formData.tax_class_id;
            }

            const response = await fetch(`/api/fee-rules?context=${context}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(submitData),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to create fee rule');
            }

            // Show success message with fee application results
            let successMessage = 'Fee rule created successfully!';

            if (result.feeApplication) {
                if (result.feeApplication.attempted) {
                    if (result.feeApplication.success) {
                        successMessage += ` Applied ${result.feeApplication.appliedFeesCount} fee(s) to checkout ${result.feeApplication.checkoutId}.`;
                    } else {
                        successMessage += ` Note: Fee application to recent checkout failed (${result.feeApplication.errorsCount} error(s)).`;
                    }
                } else {
                    successMessage += ` ${result.feeApplication.reason}.`;
                }
            }

            // Log success message and any errors
            console.log(successMessage);
            if (result.feeApplication?.details?.errors?.length > 0) {
                console.warn('Fee application errors:', result.feeApplication.details.errors);
            }

            // Redirect back to order-fee page on success
            router.push('/order-fee');
        } catch (error) {
            console.error('Error creating fee rule:', error);
            setSubmitError(error.message || 'An error occurred while creating the fee rule');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        router.push('/order-fee');
    };

    if (!context) {
        return <Loading />;
    }

    return (
        <Box>
            <Flex justifyContent="space-between" alignItems="center" marginBottom="large">
                <H1>Create Fee Rule</H1>
                <Button variant="subtle" onClick={handleCancel}>
                    Cancel
                </Button>
            </Flex>

            {submitError && (
                <Box marginBottom="medium">
                    <ErrorMessage error={new Error(submitError)} />
                </Box>
            )}

            <Panel>
                <Form onSubmit={handleSubmit}>
                    <FormGroup>
                        <Input
                            label="Name"
                            name="name"
                            placeholder="Enter fee rule name"
                            required
                            value={formData.name}
                            error={errors.name || validationError}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            onBlur={handleNameBlur}
                            disabled={isValidating}
                        />
                        {isValidating && (
                            <Text color="secondary60" marginTop="xSmall">
                                Checking name availability...
                            </Text>
                        )}
                    </FormGroup>

                    <FormGroup>
                        <Select
                            label="Type"
                            name="type"
                            required
                            value={formData.type}
                            onOptionChange={(value) => handleInputChange('type', value as 'percentage' | 'fixed')}
                            options={[
                                { value: 'percentage', content: 'Percentage' },
                                { value: 'fixed', content: 'Fixed Amount' },
                            ]}
                        />
                    </FormGroup>

                    <FormGroup>
                        <Input
                            label="Display Name"
                            name="display_name"
                            placeholder="Enter display name for customers"
                            required
                            value={formData.display_name}
                            error={errors.display_name}
                            onChange={(e) => handleInputChange('display_name', e.target.value)}
                        />
                    </FormGroup>

                    <FormGroup>
                        <Input
                            label={`Cost ${formData.type === 'percentage' ? '(%)' : '($)'}`}
                            name="cost"
                            type="number"
                            min="0"
                            step={formData.type === 'percentage' ? '0.01' : '0.01'}
                            placeholder={formData.type === 'percentage' ? 'Enter percentage (e.g., 5.5)' : 'Enter fixed amount (e.g., 10.00)'}
                            required
                            value={formData.cost}
                            error={errors.cost}
                            onChange={(e) => handleInputChange('cost', parseFloat(e.target.value) || 0)}
                        />
                    </FormGroup>

                    {/* <FormGroup>
                        <Input
                            label="Source"
                            name="source"
                            placeholder="Enter source (e.g., APP, STORE)"
                            required
                            value={formData.source}
                            error={errors.source}
                            onChange={(e) => handleInputChange('source', e.target.value)}
                        />
                        <Text color="secondary60" marginTop="xSmall">
                            Required by BigCommerce API. Use "APP" for application-generated fees.
                        </Text>
                    </FormGroup> */}

                    <FormGroup>
                        <Select
                            label="Tax Class"
                            name="tax_class_id"
                            value={formData.tax_class_id === '' ? '' : formData.tax_class_id.toString()}
                            onOptionChange={(value) => handleInputChange(
                                'tax_class_id', 
                                value === '' ? '' : parseInt(value)
                            )}
                            options={[
                                { value: '', content: 'Choose tax class (optional)' },
                                ...(taxClasses || []).map(taxClass => ({
                                    value: taxClass.id.toString(),
                                    content: taxClass.name
                                }))
                            ]}
                            error={errors.tax_class_id}
                        />
                        {taxClassesLoading && (
                            <Text color="secondary60" marginTop="xSmall">
                                Loading tax classes...
                            </Text>
                        )}
                    </FormGroup>

                    <CheckboxList
                        label="Categories"
                        items={categories.map(cat => ({ id: cat.category_id, name: cat.name }))}
                        selectedIds={formData.selectedCategories}
                        onChange={(selectedIds) => handleInputChange('selectedCategories', selectedIds)}
                        loading={categoriesLoading}
                        error={categoriesError}
                    />

                    <CheckboxList
                        label="Brands"
                        items={brands.map(brand => ({ id: brand.id, name: brand.name }))}
                        selectedIds={formData.selectedBrands}
                        onChange={(selectedIds) => handleInputChange('selectedBrands', selectedIds)}
                        loading={brandsLoading}
                        error={brandsError}
                    />

                    <CheckboxList
                        label="Customer Groups"
                        items={customerGroups.map(group => ({ id: group.id, name: group.name }))}
                        selectedIds={formData.selectedCustomerGroups}
                        onChange={(selectedIds) => handleInputChange('selectedCustomerGroups', selectedIds)}
                        loading={customerGroupsLoading}
                        error={customerGroupsError}
                    />

                    <CheckboxList
                        label="Channels"
                        items={channels.map(channel => ({ id: channel.id, name: channel.name }))}
                        selectedIds={formData.selectedChannels}
                        onChange={(selectedIds) => handleInputChange('selectedChannels', selectedIds)}
                        loading={channelsLoading}
                        error={channelsError}
                    />

                    <ProductFilter
                        selectedIds={formData.selectedProducts}
                        onChange={(selectedIds) => handleInputChange('selectedProducts', selectedIds)}
                    />

                    <FormGroup>
                        <Checkbox
                            label="Active"
                            checked={formData.active}
                            onChange={(e) => handleInputChange('active', e.target.checked)}
                        />
                        <Text color="secondary60" marginTop="xSmall">
                            Check to make this fee rule active and applicable to orders
                        </Text>
                    </FormGroup>

                    <Flex justifyContent="flex-end" marginTop="large">
                        <Button
                            variant="subtle"
                            marginRight="medium"
                            onClick={handleCancel}
                            disabled={isSubmitting}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting || isValidating || !!validationError}
                        >
                            {isSubmitting ? 'Creating...' : 'Save Fee Rule'}
                        </Button>
                    </Flex>
                </Form>
            </Panel>
        </Box>
    );
};

export default CreateFee;
